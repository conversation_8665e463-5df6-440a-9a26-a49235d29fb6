import type { UseChatHelpers } from "@ai-sdk/react";
import ArrowUp from "@untitled-ui/icons-react/build/esm/ArrowUp";
import File02 from "@untitled-ui/icons-react/build/esm/File02";
import classNames from "classnames";
import { LazyTooltip } from "components/LazyTooltip";
import { motion, useMotionValue, useTransform } from "framer-motion";
import { nanoid } from "nanoid";
import React from "react";

const randomArr = [...Array(6)].map((_) => nanoid(5));

type Props = {
  setSelectedTool: React.Dispatch<React.SetStateAction<string | undefined>>;
  isAnimating: boolean;
  append: UseChatHelpers["append"];
};

export const ReadingLevelSelector: React.FC<Props> = (props) => {
  const { setSelectedTool, append, isAnimating } = props;

  const LEVELS = [
    "Elementary",
    "Middle School",
    "Keep current level",
    "High School",
    "College",
    "Graduate",
  ];

  const y = useMotionValue(-40 * 2);
  const dragConstraints = 5 * 40 + 2;
  const yToLevel = useTransform(y, [0, -dragConstraints], [0, 5]);

  const [currentLevel, setCurrentLevel] = React.useState<number>(2);
  const [hasUserSelectedLevel, setHasUserSelectedLevel] =
    React.useState<boolean>(false);

  React.useEffect(() => {
    const unsubscribe = yToLevel.on("change", (latest) => {
      const level = Math.min(5, Math.max(0, Math.round(Math.abs(latest))));
      setCurrentLevel(level);
    });

    return () => unsubscribe();
  }, [yToLevel]);

  return (
    <div className="relative flex flex-col items-center justify-end">
      {randomArr.map((id) => (
        <motion.div
          key={id}
          className="flex size-[40px] flex-row items-center justify-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ delay: 0.1 }}
        >
          <div className="size-2 rounded-full" />
        </motion.div>
      ))}

      <LazyTooltip
        text={isAnimating === false ? LEVELS[currentLevel] : undefined}
      >
        <motion.div
          className={classNames(
            "bg-background absolute flex flex-row items-center rounded-full border p-3",
            {
              "bg-primary text-primary-foreground": currentLevel !== 2,
              "bg-background text-foreground": currentLevel === 2,
            },
          )}
          style={{ y }}
          drag="y"
          dragElastic={0}
          dragMomentum={false}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          transition={{ duration: 0.1 }}
          dragConstraints={{ top: -dragConstraints, bottom: 0 }}
          onDragStart={() => {
            setHasUserSelectedLevel(false);
          }}
          onDragEnd={() => {
            if (currentLevel === 2) {
              setSelectedTool(undefined);
            } else {
              setHasUserSelectedLevel(true);
            }
          }}
          onClick={() => {
            if (currentLevel !== 2 && hasUserSelectedLevel) {
              append({
                role: "user",
                content: `Please adjust the reading level to ${LEVELS[currentLevel]} level.`,
              });

              setSelectedTool(undefined);
            }
          }}
        >
          {currentLevel === 2 ? <File02 /> : <ArrowUp className="size-4" />}
        </motion.div>
      </LazyTooltip>
    </div>
  );
};
