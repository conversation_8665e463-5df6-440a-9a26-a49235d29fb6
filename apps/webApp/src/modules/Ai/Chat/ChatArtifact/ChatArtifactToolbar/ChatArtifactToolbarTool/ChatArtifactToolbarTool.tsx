import type { UseChatHelpers } from "@ai-sdk/react";
import ArrowUp from "@untitled-ui/icons-react/build/esm/ArrowUp";
import classNames from "classnames";
import { LazyTooltip } from "components/LazyTooltip";
import { motion } from "framer-motion";
import React from "react";

type Props = {
  description: string;
  icon: React.ReactNode;
  selectedTool?: string;
  setSelectedTool: React.Dispatch<React.SetStateAction<string | undefined>>;
  isToolbarVisible?: boolean;
  setIsToolbarVisible?: React.Dispatch<React.SetStateAction<boolean>>;
  isAnimating: boolean;
  append: UseChatHelpers["append"];
  onClick: ({
    appendMessage,
  }: {
    appendMessage: UseChatHelpers["append"];
  }) => void;
};

export const ChatArtifactToolbarTool: React.FC<Props> = (props) => {
  const {
    description,
    icon,
    selectedTool,
    setSelectedTool,
    isToolbarVisible,
    setIsToolbarVisible,
    isAnimating,
    append,
    onClick,
  } = props;

  const [isHovered, setIsHovered] = React.useState(false);

  React.useEffect(() => {
    if (selectedTool !== description) {
      setIsHovered(false);
    }
  }, [selectedTool, description]);

  const handleSelect = () => {
    if (isToolbarVisible === false && setIsToolbarVisible !== undefined) {
      setIsToolbarVisible(true);
      return;
    }

    if (selectedTool === undefined) {
      setIsHovered(true);
      setSelectedTool(description);
      return;
    }

    if (selectedTool !== description) {
      setSelectedTool(description);
    } else {
      setSelectedTool(undefined);
      onClick({ appendMessage: append });
    }
  };

  const text = React.useMemo(() => {
    if (isHovered === false || isAnimating) {
      return undefined;
    }

    return description;
  }, [description, isAnimating, isHovered]);

  return (
    <LazyTooltip text={text}>
      <motion.div
        className={classNames("rounded-full p-3", {
          "bg-primary !text-primary-foreground": selectedTool === description,
        })}
        onHoverStart={() => {
          setIsHovered(true);
        }}
        onHoverEnd={() => {
          if (selectedTool !== description) {
            setIsHovered(false);
          }
        }}
        onKeyDown={(event) => {
          if (event.key === "Enter") {
            handleSelect();
          }
        }}
        initial={{ scale: 1, opacity: 0 }}
        animate={{ opacity: 1, transition: { delay: 0.1 } }}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
        exit={{
          scale: 0.9,
          opacity: 0,
          transition: { duration: 0.1 },
        }}
        onClick={() => {
          handleSelect();
        }}
      >
        {selectedTool === description ? <ArrowUp /> : icon}
      </motion.div>
    </LazyTooltip>
  );
};
