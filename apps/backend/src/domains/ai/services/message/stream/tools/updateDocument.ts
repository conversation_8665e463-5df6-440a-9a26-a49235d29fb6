import type { DataStreamWriter } from "ai";
import { tool } from "ai";
import { z } from "zod";

import type { AiServicesDependencies } from "../../../dependencies.js";

type UpdateDocumentPayload = {
  workspaceUuid: string;
  userUuid: string;
  dataStream: DataStreamWriter;
};

export const updateDocument = (
  dependencies: AiServicesDependencies,
  payload: UpdateDocumentPayload,
) => {
  const { artifacts, repositories } = dependencies;
  const { workspaceUuid, dataStream } = payload;

  return tool({
    description: "Update a document with the given description.",
    parameters: z.object({
      id: z.string().describe("The ID of the document to update"),
      description: z
        .string()
        .describe("The description of changes that need to be made"),
    }),
    execute: async ({ id, description }) => {
      const document = await repositories.pg.document.get({
        workspaceUuid,
        uuid: id,
      });

      if (document === undefined) {
        throw new Error("Document not found");
      }

      dataStream.writeData({
        type: "clear",
        content: document.title,
      });

      const documentHandler = artifacts.document[document.kind];

      if (documentHandler === undefined) {
        throw new Error(`No document handler found for kind: ${document.kind}`);
      }

      await documentHandler.handler.onUpdate({
        document,
        description,
        dataStream,
      });

      dataStream.writeData({ type: "finish", content: "" });

      return {
        id,
        title: document.title,
        kind: document.kind,
        content: "The document has been updated successfully.",
      };
    },
  });
};
