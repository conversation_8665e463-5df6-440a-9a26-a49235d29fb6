import type { DataStreamWriter } from "ai";
import { tool } from "ai";
import { v4 as uuidv4 } from "uuid";
import { z } from "zod";

import type { AiServicesDependencies } from "../../../dependencies.js";

type CreateDocumentPayload = {
  workspaceUuid: string;
  userUuid: string;
  dataStream: DataStreamWriter;
};

export const createDocumentTool = (
  dependencies: AiServicesDependencies,
  payload: CreateDocumentPayload,
) => {
  const { artifacts } = dependencies;
  const { dataStream } = payload;

  return tool({
    description:
      "Create a document for a writing or content creation activities. This tool will call other functions that will generate the contents of the document based on the title and kind.",
    parameters: z.object({
      title: z.string(),
      kind: z.enum(["text", "code", "sheet", "image"]),
    }),
    execute: async ({ title, kind }) => {
      const id = uuidv4();

      dataStream.writeData({
        type: "kind",
        content: kind,
      });

      dataStream.writeData({
        type: "id",
        content: id,
      });

      dataStream.writeData({
        type: "title",
        content: title,
      });

      dataStream.writeData({
        type: "clear",
        content: "",
      });

      if (typeof kind !== "string") {
        throw new Error(`Invalid kind`);
      }

      const documentHandler = artifacts.document[kind];

      if (documentHandler === undefined) {
        throw new Error(`No document handler found for kind: ${kind}`);
      }

      await documentHandler.handler.onCreate({
        id,
        title,
        dataStream,
      });

      dataStream.writeData({ type: "finish", content: "" });

      return {
        id,
        title,
        kind,
        content: "A document was created and is now visible to the user.",
      };
    },
  });
};
