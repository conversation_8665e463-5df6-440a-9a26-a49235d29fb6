import { createAnthropic } from "@ai-sdk/anthropic";
import { createOpenAI } from "@ai-sdk/openai";
import type { Provider } from "ai";
import {
  appendResponseMessages,
  convertToCoreMessages,
  createDataStream,
  experimental_createMCPClient,
  experimental_generateImage,
  generateObject,
  generateText,
  smoothStream,
  streamObject,
  streamText,
} from "ai";

export type {
  DataStreamWriter as AiDataStreamWriter,
  Tool as AiTool,
  ToolSet as AiToolSet,
} from "ai";
export {
  type Message as AiMessage,
  type StreamTextResult as AiStreamTextResult,
  jsonSchema,
} from "ai";

import type { Logger } from "../logger/index.js";
import {
  vercelUiMessageSchema,
  vercelUseChatPayloadSchema,
} from "./vercelSchemas.js";

export type VercelAiProvider = "openai" | "anthropic";

export type VercelAiClientDependencies = {
  logger: Logger;
  config: {
    openai: {
      apiKey: string;
    };
    anthropic: {
      apiKey: string;
    };
  };
};

export type VercelAiClient = {
  getProvider: (
    provider: VercelAiProvider,
    config?: { apiKey: string },
  ) => Provider;
  generateText: typeof generateText;
  generateObject: typeof generateObject;
  generateImage: typeof experimental_generateImage;
  streamText: typeof streamText;
  streamObject: typeof streamObject;
  createDataStream: typeof createDataStream;
  convertToCoreMessages: typeof convertToCoreMessages;
  appendResponseMessages: typeof appendResponseMessages;
  smoothStream: typeof smoothStream;
  createMCPClient: typeof experimental_createMCPClient;
  schemas: {
    vercelUseChatPayloadSchema: typeof vercelUseChatPayloadSchema;
    vercelUiMessageSchema: typeof vercelUiMessageSchema;
  };
};

export const buildVercelAiClient = (
  dependencies: VercelAiClientDependencies,
): VercelAiClient => {
  return {
    getProvider: (provider, config) => {
      if (provider === "openai") {
        return createOpenAI({
          apiKey:
            config !== undefined
              ? config.apiKey
              : dependencies.config.openai.apiKey,
          compatibility: "strict",
        });
      }

      if (provider === "anthropic") {
        return createAnthropic({
          apiKey:
            config !== undefined
              ? config.apiKey
              : dependencies.config.openai.apiKey,
        }) as Provider;
      }

      throw new Error("Invalid provider");
    },
    generateText,
    generateObject,
    generateImage: experimental_generateImage,
    streamText,
    streamObject,
    createDataStream,
    convertToCoreMessages,
    appendResponseMessages,
    smoothStream,
    createMCPClient: experimental_createMCPClient,
    schemas: {
      vercelUseChatPayloadSchema,
      vercelUiMessageSchema,
    },
  };
};
